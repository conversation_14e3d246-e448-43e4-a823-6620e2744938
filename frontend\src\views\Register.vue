<template>
  <div class="register-page">
    <!-- 顶部导航栏 -->
    <header class="top-nav">
      <div class="nav-container">
        <router-link to="/" class="logo">
          <span class="logo-icon">☕</span>
          <span class="logo-text">咖啡豆溯源平台</span>
        </router-link>
        <nav class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/trace" class="nav-link">溯源查询</router-link>
          <router-link to="/login" class="nav-link">登录</router-link>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="register-container">
        <!-- 注册表单 -->
        <div class="register-card">
          <div class="register-header">
            <h1>用户注册</h1>
            <p>创建账户，加入咖啡豆溯源生态系统</p>
          </div>

          <form class="register-form" @submit.prevent="handleRegister">
            <div class="form-group">
              <label class="form-label">用户名</label>
              <input
                v-model="registerForm.username"
                type="text"
                placeholder="请输入真实姓名（仅支持中文）"
                class="form-input"
                pattern="[\u4e00-\u9fa5]+"
                title="用户名只能包含中文字符"
                required
              />
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">密码</label>
                <input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="请输入密码（至少6位）"
                  class="form-input"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">确认密码</label>
                <input
                  v-model="registerForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入密码"
                  class="form-input"
                  required
                />
              </div>
            </div>



            <div class="form-group">
              <label class="form-label">手机号码（可选）</label>
              <input
                v-model="registerForm.phone"
                type="tel"
                placeholder="请输入手机号码"
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label class="form-label">选择角色</label>
              <div class="role-selector">
                <label class="role-option" :class="{ active: registerForm.role === 'grower' }">
                  <input type="radio" v-model="registerForm.role" value="grower" />
                  <div class="role-content">
                    <span class="role-icon">🌱</span>
                    <span class="role-text">种植者</span>
                  </div>
                </label>
                <label class="role-option" :class="{ active: registerForm.role === 'processor' }">
                  <input type="radio" v-model="registerForm.role" value="processor" />
                  <div class="role-content">
                    <span class="role-icon">⚙️</span>
                    <span class="role-text">加工商</span>
                  </div>
                </label>
                <label class="role-option" :class="{ active: registerForm.role === 'distributor' }">
                  <input type="radio" v-model="registerForm.role" value="distributor" />
                  <div class="role-content">
                    <span class="role-icon">🚚</span>
                    <span class="role-text">经销商</span>
                  </div>
                </label>
                <label class="role-option" :class="{ active: registerForm.role === 'consumer' }">
                  <input type="radio" v-model="registerForm.role" value="consumer" />
                  <div class="role-content">
                    <span class="role-icon">👤</span>
                    <span class="role-text">消费者</span>
                  </div>
                </label>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">公司名称（可选）</label>
              <input
                v-model="registerForm.companyName"
                type="text"
                placeholder="请输入公司名称"
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label class="checkbox-wrapper">
                <input type="checkbox" required />
                <span class="checkmark"></span>
                我已阅读并同意
                <a href="#" class="terms-link">《用户协议》</a>
                和
                <a href="#" class="terms-link">《隐私政策》</a>
              </label>
            </div>

            <button
              type="submit"
              class="register-button"
              :disabled="loading"
              @click="handleRegister"
            >
              {{ loading ? '注册中...' : '立即注册' }}
            </button>
          </form>

          <div class="register-footer">
            <p class="login-prompt">
              已有账户？
              <router-link to="/login" class="login-link">立即登录</router-link>
            </p>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)

const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  phone: '',
  role: '',
  companyName: ''
})

const handleRegister = async () => {

  // 基本验证
  if (!registerForm.username || !registerForm.password || !registerForm.role) {
    alert('请填写必填信息')
    return
  }

  // 验证用户名格式（只支持中文）
  const usernameRegex = /^[\u4e00-\u9fa5]+$/
  if (!usernameRegex.test(registerForm.username)) {
    alert('用户名只能包含中文字符，请输入真实姓名')
    return
  }

  if (registerForm.username.length < 2) {
    alert('姓名长度至少2个字符')
    return
  }

  if (registerForm.username.length > 10) {
    alert('姓名长度不能超过10个字符')
    return
  }

  if (registerForm.password !== registerForm.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }

  if (registerForm.password.length < 6) {
    alert('密码长度至少6位')
    return
  }

  loading.value = true

  try {
    // 准备注册数据
    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      role: registerForm.role,
      real_name: registerForm.username // 使用用户名作为真实姓名
    }

    // 只有当字段有值时才添加到请求数据中
    if (registerForm.phone && registerForm.phone.trim() !== '') {
      registerData.phone = registerForm.phone.trim()
    }

    if (registerForm.companyName && registerForm.companyName.trim() !== '') {
      registerData.company_name = registerForm.companyName.trim()
    }



    // 发送注册请求到后端
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
      },
      body: JSON.stringify(registerData)
    })

    const result = await response.json()


    if (result.success) {
      // 清除任何残留的认证状态
      await authStore.logout()

      alert('注册成功！请等待管理员审核')
      router.push('/login')
    } else {
      // 显示详细的错误信息
      if (result.errors && result.errors.length > 0) {
        const errorMessages = result.errors.map(err => err.msg).join('\n')
        alert(`注册失败：\n${errorMessages}`)
      } else {
        alert(result.message || '注册失败，请稍后重试')
      }
    }
  } catch (error) {
    console.error('注册失败:', error)
    alert('网络错误，请检查网络连接后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-nav {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  width: 100%;
  padding: 0 40px;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 700;
  font-size: 1.2rem;
}

.logo-icon {
  font-size: 1.8rem;
}

.nav-links {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: #7f8c8d;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #3498db;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.register-container {
  width: 100%;
  padding: 0 40px;
}

/* 注册卡片 */
.register-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  padding: 60px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 1.8rem;
  font-weight: 700;
}

.register-header p {
  color: #7f8c8d;
  font-size: 0.95rem;
  margin: 0;
}

.register-form {
  margin-bottom: 25px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 6px;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 角色选择器 */
.role-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.role-option {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  position: relative;
}

.role-option input[type="radio"] {
  display: none;
}

.role-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  gap: 8px;
}

.role-icon {
  font-size: 1.5rem;
}

.role-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #2c3e50;
}

.role-option:hover,
.role-option.active {
  border-color: #3498db;
  background: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.checkbox-wrapper input[type="checkbox"] {
  margin: 0;
  margin-top: 2px;
}

.terms-link {
  color: #3498db;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.register-button {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.register-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #2980b9, #3498db);
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.register-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.login-prompt {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.login-link {
  color: #3498db;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}


</style>
