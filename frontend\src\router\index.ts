import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/Home.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/Register.vue')
    },

    {
      path: '/admin',
      name: 'admin',
      component: () => import('@/views/admin/AdminLayout.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: '',
          name: 'admin-dashboard',
          component: () => import('@/views/admin/Dashboard.vue')
        },
        {
          path: 'dashboard',
          name: 'admin-dashboard-alt',
          component: () => import('@/views/admin/Dashboard.vue')
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('@/views/admin/UserManagement.vue')
        },
        {
          path: 'pending',
          name: 'admin-pending',
          component: () => import('@/views/admin/PendingApprovals.vue')
        }
      ]
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/user/UserLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'user-dashboard',
          component: () => import('@/views/user/Dashboard.vue')
        },
        {
          path: 'grower',
          name: 'grower-dashboard',
          component: () => import('@/views/user/GrowerDashboard.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/batches',
          name: 'grower-batches',
          component: () => import('@/views/user/grower/BatchManagement.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/planting',
          name: 'grower-planting',
          component: () => import('@/views/user/grower/SimplePlantingRecords.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/harvest',
          name: 'grower-harvest',
          component: () => import('@/views/user/grower/HarvestManagement.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/shipping',
          name: 'grower-shipping',
          component: () => import('@/views/user/grower/ShippingManagement.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/logistics',
          name: 'grower-logistics',
          component: () => import('@/views/user/grower/LogisticsTracking.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/blockchain',
          name: 'grower-blockchain',
          component: () => import('@/views/user/grower/BlockchainManagement.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'grower/certificates',
          name: 'grower-certificates',
          component: () => import('@/views/user/grower/DigitalCertificates.vue'),
          meta: { requiresRole: 'grower' }
        },
        {
          path: 'processor',
          name: 'processor-dashboard',
          component: () => import('@/views/user/ProcessorDashboard.vue'),
          meta: { requiresRole: 'processor' }
        },
        {
          path: 'distributor',
          name: 'distributor-dashboard',
          component: () => import('@/views/user/DistributorDashboard.vue'),
          meta: { requiresRole: 'distributor' }
        },
        {
          path: 'consumer',
          name: 'consumer-dashboard',
          component: () => import('@/views/user/ConsumerDashboard.vue'),
          meta: { requiresRole: 'consumer' }
        }
      ]
    },
    {
      path: '/trace/:batchId?',
      name: 'trace',
      component: () => import('@/views/TraceabilityQuery.vue')
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果没有认证，尝试从本地存储恢复登录状态
    if (!authStore.isAuthenticated) {
      const authResult = await authStore.checkAuth()
      if (!authResult) {
        next({ name: 'login', query: { redirect: to.fullPath } })
        return
      }
    } else {
      // 如果已经认证，但可能需要刷新用户信息
      await authStore.checkAuth()
    }

    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next({ name: 'home' })
      return
    }

    // 检查角色权限
    if (to.meta.requiresRole && authStore.user?.role !== to.meta.requiresRole) {
      next({ name: 'user-dashboard' })
      return
    }
  }

  // 如果已登录用户访问登录页，先验证token有效性再重定向
  if (to.name === 'login' && authStore.isAuthenticated) {
    // 验证token是否仍然有效
    const authResult = await authStore.checkAuth()
    if (authResult) {
      // token有效，重定向到对应的仪表板
      if (authStore.isAdmin) {
        next({ name: 'admin-dashboard' })
      } else {
        next({ name: 'user-dashboard' })
      }
      return
    }
    // token无效，清除认证状态，继续访问登录页
  }

  next()
})

export default router
