const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// 验证用户token
const authenticateUser = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (!decoded.userId) {
      return res.status(401).json({
        success: false,
        message: '无效的用户令牌'
      });
    }

    // 查询用户信息
    const users = await query(
      'SELECT id, username, role, status, is_online FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    // 检查用户状态
    if (user.status !== 'approved') {
      return res.status(403).json({
        success: false,
        message: '账户未激活或已被暂停'
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('用户认证失败:', error);
    res.status(500).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 验证管理员token
const authenticateAdmin = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (!decoded.adminId) {
      return res.status(401).json({
        success: false,
        message: '无效的管理员令牌'
      });
    }

    // 查询管理员信息
    const admins = await query(
      'SELECT id, username, real_name, permissions, is_online FROM admins WHERE id = ?',
      [decoded.adminId]
    );

    if (admins.length === 0) {
      return res.status(401).json({
        success: false,
        message: '管理员不存在'
      });
    }

    // 将管理员信息添加到请求对象
    const admin = admins[0];
    // 解析权限JSON字符串
    if (typeof admin.permissions === 'string') {
      try {
        admin.permissions = JSON.parse(admin.permissions);
      } catch (e) {
        console.error('解析管理员权限JSON失败:', e);
        admin.permissions = {};
      }
    }
    req.admin = admin;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('管理员认证失败:', error);
    res.status(500).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 验证用户角色
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的用户'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 验证管理员权限
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: '未认证的管理员'
      });
    }

    let permissions = req.admin.permissions;

    // 如果permissions是字符串，尝试解析为JSON
    if (typeof permissions === 'string') {
      try {
        permissions = JSON.parse(permissions);
      } catch (error) {
        console.error('解析权限JSON失败:', error);
        return res.status(403).json({
          success: false,
          message: '权限配置错误'
        });
      }
    }

    // 检查权限：如果有all权限或者有特定权限
    if (!permissions || (permissions.all !== true && permissions[permission] !== true)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 通用认证中间件（支持用户和管理员）
const authenticate = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.userId) {
      // 用户认证
      const users = await query(
        'SELECT id, username, role, status, is_online FROM users WHERE id = ?',
        [decoded.userId]
      );

      if (users.length === 0 || users[0].status !== 'approved') {
        return res.status(401).json({
          success: false,
          message: '用户不存在或账户未激活'
        });
      }

      req.user = users[0];
      req.userType = 'user';
    } else if (decoded.adminId) {
      // 管理员认证
      const admins = await query(
        'SELECT id, username, real_name, permissions, is_online FROM admins WHERE id = ?',
        [decoded.adminId]
      );

      if (admins.length === 0) {
        return res.status(401).json({
          success: false,
          message: '管理员不存在'
        });
      }

      // 确保权限字段是对象格式
      const admin = admins[0];
      if (typeof admin.permissions === 'string') {
        try {
          admin.permissions = JSON.parse(admin.permissions);
        } catch (error) {
          console.error('解析管理员权限JSON失败:', error);
          admin.permissions = {};
        }
      }

      req.admin = admin;
      req.userType = 'admin';
    } else {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('认证失败:', error);
    res.status(500).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 简化的日志记录（仅用于控制台输出）
const logAction = (action, resource = null) => {
  return async (req, res, next) => {
    try {
      // 只在控制台输出重要操作日志
      const importantActions = [
        'user_approved', 'user_rejected', 'user_deleted', 'user_updated'
      ];

      if (importantActions.includes(action)) {
        const operator = req.admin?.username || req.user?.username || '未知';
        console.log(`[${new Date().toISOString()}] ${operator} 执行了操作: ${action} (资源: ${resource})`);
      }

      next();
    } catch (error) {
      console.error('记录操作日志失败:', error);
      next();
    }
  };
};



module.exports = {
  authenticateUser,
  authenticateAdmin,
  requireRole,
  requirePermission,
  authenticate,
  logAction
};
