const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query, transaction } = require('../config/database');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// 用户注册
router.post('/register', [
  body('username').isLength({ min: 2, max: 10 }).withMessage('姓名长度必须在2-10个字符之间')
    .matches(/^[\u4e00-\u9fa5]+$/).withMessage('用户名只能包含中文字符，请输入真实姓名'),
  body('password').isLength({ min: 6 }).withMessage('密码长度至少6位'),

  body('role').isIn(['grower', 'processor', 'distributor', 'consumer']).withMessage('无效的角色类型'),
  body('real_name').notEmpty().withMessage('真实姓名不能为空'),
  // phone 字段完全可选，不进行格式验证
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password, phone, role, real_name, company_name, address } = req.body;

    // 额外验证用户名只包含中文字符
    const chineseOnlyRegex = /^[\u4e00-\u9fa5]+$/;
    if (!chineseOnlyRegex.test(username)) {
      return res.status(400).json({
        success: false,
        message: '用户名只能包含中文字符，请输入真实姓名'
      });
    }

    // 验证用户名长度
    if (username.length < 2 || username.length > 10) {
      return res.status(400).json({
        success: false,
        message: '姓名长度必须在2-10个字符之间'
      });
    }

    // 处理可选字段，将空字符串转换为null
    const processedPhone = phone && phone.trim() !== '' ? phone.trim() : null;
    const processedCompanyName = company_name && company_name.trim() !== '' ? company_name.trim() : null;

    // 如果提供了手机号码，验证格式
    if (processedPhone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(processedPhone)) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的手机号码'
        });
      }
    }

    // 检查用户名是否已存在
    const existingUser = await query('SELECT id FROM users WHERE username = ?', [username]);

    if (existingUser.length > 0) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 插入新用户
    const result = await query(
      `INSERT INTO users (username, password, phone, role, real_name, company_name, address, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')`,
      [
        username,
        hashedPassword,
        processedPhone,
        role,
        real_name,
        processedCompanyName,
        address || null
      ]
    );

    res.status(201).json({
      success: true,
      message: '注册成功，请等待管理员审核',
      data: {
        userId: result.insertId,
        username,
        role,
        status: 'pending'
      }
    });

  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      success: false,
      message: '注册失败，请稍后重试'
    });
  }
});

// 用户登录
router.post('/login', [
  body('username').notEmpty().withMessage('用户名不能为空'),
  body('password').notEmpty().withMessage('密码不能为空')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;
    console.log('登录请求:', { username, password: password.length + '位密码' });

    // 查找用户
    const users = await query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    console.log('查找用户结果:', users.length > 0 ? '找到用户' : '未找到用户');

    if (users.length === 0) {
      console.log('用户不存在:', username);
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const user = users[0];
    console.log('用户状态:', user.status);

    // 检查账户状态
    if (user.status !== 'approved') {
      console.log('账户状态不是approved:', user.status);
      let message = '账户未激活';
      switch (user.status) {
        case 'pending':
          message = '账户待审核，请等待管理员审批';
          break;
        case 'rejected':
          message = '账户已被拒绝，请联系管理员';
          break;
        case 'suspended':
          message = '账户已被暂停，请联系管理员';
          break;
      }
      return res.status(403).json({
        success: false,
        message
      });
    }

    // 验证密码
    console.log('开始验证密码...');
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('密码验证结果:', isPasswordValid);
    if (!isPasswordValid) {
      console.log('密码验证失败');
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 更新最后登录时间和在线状态
    await query(
      'UPDATE users SET last_login = NOW(), is_online = TRUE WHERE id = ?',
      [user.id]
    );

    // 创建会话记录
    const sessionToken = jwt.sign(
      { userId: user.id, timestamp: Date.now() },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    await query(
      `INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at) 
       VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))`,
      [user.id, sessionToken, req.ip, req.get('User-Agent')]
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          real_name: user.real_name,
          company_name: user.company_name,
          last_login: user.last_login
        }
      }
    });

  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

// 管理员登录
router.post('/admin/login', [
  body('username').notEmpty().withMessage('用户名不能为空'),
  body('password').notEmpty().withMessage('密码不能为空')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查找管理员
    const admins = await query(
      'SELECT * FROM admins WHERE username = ?',
      [username]
    );

    if (admins.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const admin = admins[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, admin.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        adminId: admin.id, 
        username: admin.username, 
        role: 'admin' 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 更新最后登录时间和在线状态
    await query(
      'UPDATE admins SET last_login = NOW(), is_online = TRUE WHERE id = ?',
      [admin.id]
    );

    // 创建会话记录
    const sessionToken = jwt.sign(
      { adminId: admin.id, timestamp: Date.now() },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    await query(
      `INSERT INTO user_sessions (admin_id, session_token, ip_address, user_agent, expires_at) 
       VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))`,
      [admin.id, sessionToken, req.ip, req.get('User-Agent')]
    );

    // 确保权限字段是对象格式
    let permissions = admin.permissions;
    if (typeof permissions === 'string') {
      try {
        permissions = JSON.parse(permissions);
      } catch (error) {
        console.error('解析管理员权限JSON失败:', error);
        permissions = {};
      }
    }

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          real_name: admin.real_name,
          permissions: permissions,
          last_login: admin.last_login
        }
      }
    });

  } catch (error) {
    console.error('管理员登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.userId) {
        // 更新用户在线状态
        await query('UPDATE users SET is_online = FALSE WHERE id = ?', [decoded.userId]);
        
        // 更新会话状态
        await query(
          'UPDATE user_sessions SET is_active = FALSE, logout_time = NOW() WHERE user_id = ? AND is_active = TRUE',
          [decoded.userId]
        );
      } else if (decoded.adminId) {
        // 更新管理员在线状态
        await query('UPDATE admins SET is_online = FALSE WHERE id = ?', [decoded.adminId]);
        
        // 更新会话状态
        await query(
          'UPDATE user_sessions SET is_active = FALSE, logout_time = NOW() WHERE admin_id = ? AND is_active = TRUE',
          [decoded.adminId]
        );
      }
    }

    res.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出失败:', error);
    res.json({
      success: true,
      message: '登出成功'
    });
  }
});

// 获取当前用户信息
router.get('/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.userId) {
      const users = await query(
        'SELECT id, username, role, real_name, company_name, address, status, last_login, is_online FROM users WHERE id = ?',
        [decoded.userId]
      );
      
      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: {
          user: users[0],
          userType: 'user'
        }
      });
    } else if (decoded.adminId) {
      const admins = await query(
        'SELECT id, username, real_name, permissions, last_login, is_online FROM admins WHERE id = ?',
        [decoded.adminId]
      );
      
      if (admins.length === 0) {
        return res.status(404).json({
          success: false,
          message: '管理员不存在'
        });
      }

      // 确保权限字段是对象格式
      const admin = admins[0];
      if (typeof admin.permissions === 'string') {
        try {
          admin.permissions = JSON.parse(admin.permissions);
        } catch (error) {
          console.error('解析管理员权限JSON失败:', error);
          admin.permissions = {};
        }
      }

      res.json({
        success: true,
        data: {
          admin: admin,
          userType: 'admin'
        }
      });
    } else {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(401).json({
      success: false,
      message: '认证失败'
    });
  }
});

module.exports = router;
